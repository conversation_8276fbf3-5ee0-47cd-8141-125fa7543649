# Java Online Test Application

A Java Swing-based desktop application that provides an interactive online quiz system for testing Java programming knowledge.

## Features

- **25 multiple-choice questions** covering Java programming concepts
- **10-minute time limit** with live countdown timer
- **Interactive GUI** built with Java Swing
- **Real-time navigation** through questions
- **Automatic scoring** with pass/fail determination (60% required)
- **Detailed results** showing correct answers

## Question Topics

- Java syntax and compilation errors
- Method declarations and access modifiers
- Variable scope (static vs non-static)
- Data types and type casting
- Loops and control structures
- String manipulation
- Array operations
- Mathematical operations
- Command-line arguments

## How to Run

### Prerequisites
- Java Runtime Environment (JRE) 8 or higher

### Running the Application

1. **Compile the source code** (if needed):
   ```bash
   javac OnlineTest.java
   ```

2. **Run the application**:
   ```bash
   java OnlineTest
   ```

3. **Follow the on-screen instructions**:
   - Enter your name when prompted
   - Click "start" to begin the test
   - Navigate through questions using next/previous buttons
   - Select answers using checkboxes (A, B, C, D)
   - Click "finish" after completing all questions
   - View your results and detailed breakdown

## Project Structure

- `OnlineTest.java` - Main application source code
- `QuestionSeries` - Contains all questions, answers, and test configuration
- Compiled `.class` files for immediate execution

## Configuration

The test parameters can be modified in the `QuestionSeries` class:
- `timeLimit` - Test duration in minutes (default: 10)
- `passMark` - Minimum score to pass (default: 15/25)
- Questions and answers arrays

## Technical Details

- **Framework**: Java Swing
- **Architecture**: Event-driven GUI with inner classes
- **Threading**: Background timer for countdown
- **Layout**: BorderLayout with custom panels

## License

This project is open source and available under the MIT License.
