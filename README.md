# Java Online Test

A Java Swing desktop application for testing Java programming knowledge with 25 multiple-choice questions.

## Features
- 25 Java programming questions
- 10-minute timer
- Interactive GUI with navigation
- Automatic scoring (60% to pass)
- Detailed results display

## How to Run
```bash
# Compile (if needed)
javac OnlineTest.java

# Run
java OnlineTest
```

## Usage
1. Enter your name
2. Click "start" to begin
3. Navigate with next/previous buttons
4. Select answers (A, B, C, D)
5. Click "finish" to see results

## Requirements
- Java 8 or higher
